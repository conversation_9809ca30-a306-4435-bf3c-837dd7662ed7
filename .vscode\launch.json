{"version": "0.2.0", "configurations": [{"name": "Debug Electron Main Process", "type": "node", "request": "launch", "cwd": "${workspaceFolder}", "runtimeExecutable": "${workspaceFolder}/node_modules/.bin/electron", "windows": {"runtimeExecutable": "${workspaceFolder}/node_modules/.bin/electron.cmd"}, "args": [".", "--enable-logging"], "outputCapture": "std", "console": "integratedTerminal", "env": {"NODE_ENV": "development"}}, {"name": "Debug Electron Renderer Process", "type": "chrome", "request": "attach", "port": 9222, "webRoot": "${workspaceFolder}", "timeout": 30000}, {"name": "Debug Electron Main + Renderer", "type": "node", "request": "launch", "cwd": "${workspaceFolder}", "runtimeExecutable": "${workspaceFolder}/node_modules/.bin/electron", "windows": {"runtimeExecutable": "${workspaceFolder}/node_modules/.bin/electron.cmd"}, "args": [".", "--enable-logging", "--remote-debugging-port=9222"], "outputCapture": "std", "console": "integratedTerminal", "env": {"NODE_ENV": "development"}}], "compounds": [{"name": "Debug Electron (Main + Renderer)", "configurations": ["Debug Electron Main + Renderer", "Debug Electron Renderer Process"]}]}