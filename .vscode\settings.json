{"files.exclude": {"**/node_modules": true, "**/dist": true, "**/.git": true, "**/.DS_Store": true, "**/Thumbs.db": true}, "search.exclude": {"**/node_modules": true, "**/dist": true}, "typescript.preferences.includePackageJsonAutoImports": "on", "javascript.preferences.includePackageJsonAutoImports": "on", "emmet.includeLanguages": {"javascript": "javascriptreact"}, "editor.codeActionsOnSave": {"source.fixAll": "explicit"}, "editor.formatOnSave": true, "editor.defaultFormatter": "esbenp.prettier-vscode", "files.associations": {"*.js": "javascript"}}