const { app, BrowserWindow, ipcMain } = require('electron');
const path = require('path');
const fs = require('fs');
const express = require('express');
const pdf = require('pdf-parse');

let server;
let expressApp;

const createWindow = () => {
  const win = new BrowserWindow({
    width: 800,
    height: 600,
    webPreferences: {
      preload: path.join(__dirname, 'preload.js'),
      contextIsolation: true,
      nodeIntegration: false,
    },
  });

  win.loadFile('index.html');
  
  win.webContents.openDevTools();
};

app.whenReady().then(() => {
  ipcMain.handle('convert-pdf', async (event, filePath) => {
    const outputDir = path.join(app.getPath('userData'), 'converted-text');
    const outputHtmlFile = path.join(outputDir, 'document.html');
    const pdfFileName = path.basename(filePath);

    // Ensure the output directory exists
    if (!fs.existsSync(outputDir)) {
      fs.mkdirSync(outputDir, { recursive: true });
    }

    try {
      // 1. Read the PDF file
      const dataBuffer = fs.readFileSync(filePath);

      // 2. Parse the PDF to extract text
      const data = await pdf(dataBuffer);

      // 3. Generate clean, simple HTML
      const title = path.basename(filePath, '.pdf');

      // Split text into paragraphs based on double newlines for better structure
      const paragraphs = data.text
        .split(/\n\s*\n/) // Split by one or more empty lines
        .map(p => p.trim().replace(/\n/g, '<br>')) // Replace single newlines with <br> and trim
        .filter(p => p.length > 0) // Remove any empty paragraphs
        .map(p => `<p>${p}</p>`)
        .join('\n');

      const htmlContent = `
        <!DOCTYPE html>
        <html lang="en">
        <head>
            <meta charset="UTF-8">
            <meta name="viewport" content="width=device-width, initial-scale=1.0">
            <title>${title}</title>
            <style>
                body {
                    font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Helvetica, Arial, sans-serif;
                    line-height: 1.7;
                    background-color: #f0f0f0;
                    color: #1a1a1a;
                    max-width: 850px;
                    margin: 1rem auto;
                    padding: 2rem 4rem;
                    border-radius: 8px;
                    background-color: #fff;
                    box-shadow: 0 4px 12px rgba(0,0,0,0.05);
                }
                h1 {
                    font-size: 2.5em;
                    line-height: 1.2;
                    margin-bottom: 2rem;
                    border-bottom: 1px solid #ddd;
                    padding-bottom: 1rem;
                }
                p {
                    margin-bottom: 1.2em;
                    font-size: 1.1em;
                }
                /* Style for dark mode preferred by some screen readers */
                @media (prefers-color-scheme: dark) {
                    body {
                        background-color: #1e1e1e;
                        color: #e0e0e0;
                        border: 1px solid #333;
                    }
                    h1 {
                        border-bottom-color: #444;
                    }
                }
            </style>
        </head>
        <body>
            <h1>${title}</h1>
            ${paragraphs}
        </body>
        </html>
      `;

      // 4. Save the single HTML file
      fs.writeFileSync(outputHtmlFile, htmlContent);

      // 5. Start or restart the server to serve the new file
      if (server) {
        server.close(() => startServer(outputDir, outputHtmlFile, pdfFileName, 'document.html'));
      } else {
        startServer(outputDir, outputHtmlFile, pdfFileName, 'document.html');
      }

      // We need to return the promise result from startServer
      return new Promise(resolve => {
        global.resolveServerPromise = resolve;
      });

    } catch (error) {
      console.error('Conversion process failed:', error);
      return { success: false, message: `Failed to parse PDF: ${error.message}` };
    }
  });

  createWindow();

  app.on('activate', () => {
    if (BrowserWindow.getAllWindows().length === 0) {
      createWindow();
    }
  });
});

function startServer(directory, filePath, pdfName, htmlFileName) {
    expressApp = express();
    // Serve the directory statically
    expressApp.use(express.static(directory));

    const PORT = 3000;

    server = expressApp.listen(PORT, '127.0.0.1', () => {
        const url = `http://127.0.0.1:${PORT}/${htmlFileName}`;
        console.log(`Server started. Serving ${filePath} at ${url}`);

        // Resolve the promise to send data back to the renderer process
        if (global.resolveServerPromise) {
            global.resolveServerPromise({
                success: true,
                url: url,
                message: 'Conversion successful! Server is running.',
                domain: path.basename(pdfName, '.pdf') // Use original pdf name for domain
            });
            delete global.resolveServerPromise;
        }
    });

    server.on('error', (err) => {
        console.error('Server error:', err);
        if (global.resolveServerPromise) {
            global.resolveServerPromise({ success: false, message: 'Failed to start local server.' });
            delete global.resolveServerPromise;
        }
    });
}

app.on('window-all-closed', () => {
  if (process.platform !== 'darwin') {
    app.quit();
  }
  if (server) {
    server.close();
  }
});
