# Debugging Guide for PDF to Simple HTML Electron App

## VSCode Debug Configuration

This project includes comprehensive debugging configurations for VSCode. Here's how to use them:

### 🚀 Quick Start Debugging

1. **Open VSCode** in your project directory
2. **Press F5** or go to Run and Debug panel (Ctrl+Shift+D)
3. **Select a debug configuration** from the dropdown:

### 📋 Available Debug Configurations

#### 1. **Debug Electron Main Process**
- Debugs the main Electron process (main.js)
- Set breakpoints in main.js, server logic, IPC handlers
- Use this for debugging PDF conversion, file operations, server startup

#### 2. **Debug Electron Renderer Process**
- Debugs the renderer process (renderer.js, HTML, CSS)
- Set breakpoints in renderer.js, inspect DOM, debug UI interactions
- Use this for debugging drag-and-drop, UI updates, frontend logic

#### 3. **Debug Electron Main + Renderer**
- Starts main process with remote debugging enabled
- Allows debugging both processes simultaneously
- Best for full-stack debugging

#### 4. **Debug Electron (Main + Renderer)** [Compound]
- Automatically starts both main and renderer debugging
- **Recommended for most debugging scenarios**

### 🔧 How to Debug

#### Setting Breakpoints
1. **Main Process**: Set breakpoints directly in `main.js`
2. **Renderer Process**: Set breakpoints in `renderer.js` or use browser DevTools
3. **IPC Communication**: Set breakpoints in both main and renderer IPC handlers

#### Common Debugging Scenarios

##### PDF Conversion Issues
```javascript
// Set breakpoints in main.js around lines:
// Line 52: PDF parsing
// Line 67: HTML generation  
// Line 119: File writing
// Line 125: Error handling
```

##### UI/Drag-and-Drop Issues
```javascript
// Set breakpoints in renderer.js around lines:
// Line 15: Drop event handling
// Line 23: File validation
// Line 27: Conversion result handling
```

##### Server Issues
```javascript
// Set breakpoints in main.js around lines:
// Line 149: Server startup
// Line 155: Server success callback
// Line 171: Server error handling
```

### 🛠️ Development Environment Setup

#### Environment Variables
The debug configurations automatically set:
```bash
NODE_ENV=development
```

This enables:
- Automatic DevTools opening
- Enhanced logging
- Development-specific features

#### Console Logging
The app includes enhanced console logging:
- Main process logs appear in VSCode Debug Console
- Renderer process logs appear in both DevTools and Debug Console
- Server logs are captured and displayed

### 📊 Debugging Tools Available

#### 1. **VSCode Debug Console**
- View main process logs
- Execute Node.js commands in main process context
- Inspect variables and call stack

#### 2. **Chrome DevTools** (Renderer)
- Inspect DOM elements
- Debug JavaScript in renderer process
- Monitor network requests
- View console logs

#### 3. **Electron DevTools**
- Automatically opens in development mode
- Full Chrome DevTools functionality
- Inspect Electron-specific APIs

### 🔍 Common Debugging Commands

#### In VSCode Debug Console (Main Process):
```javascript
// Check app paths
app.getPath('userData')

// Inspect server state
server.listening

// Check file system
fs.existsSync('./some-file.pdf')
```

#### In Chrome DevTools (Renderer):
```javascript
// Check Electron API availability
window.electronAPI

// Inspect DOM elements
document.getElementById('drop-zone')

// Test file operations
console.log('Testing...')
```

### 🚨 Troubleshooting

#### Debug Session Won't Start
1. Ensure Electron is installed: `npm install`
2. Check that `node_modules/.bin/electron` exists
3. Try running `npm start` first to verify the app works

#### Breakpoints Not Hit
1. Ensure you're using the correct debug configuration
2. Check that source maps are enabled
3. Verify file paths in launch.json are correct

#### Renderer Debugging Issues
1. Make sure remote debugging port (9222) isn't in use
2. Try the compound configuration for full debugging
3. Check that DevTools are opening properly

### 📝 Debug Configuration Files

The following files have been created for debugging:

- **`.vscode/launch.json`** - Debug configurations
- **`.vscode/tasks.json`** - Build and run tasks  
- **`.vscode/settings.json`** - VSCode workspace settings

### 🎯 Pro Tips

1. **Use compound debugging** for most scenarios
2. **Set conditional breakpoints** for specific file types or error conditions
3. **Use the Debug Console** to execute code in the main process context
4. **Monitor the Express server** logs for HTTP request debugging
5. **Use `console.log()` strategically** in both main and renderer processes

### 📚 Additional Resources

- [Electron Debugging Guide](https://www.electronjs.org/docs/tutorial/debugging-main-process)
- [VSCode Node.js Debugging](https://code.visualstudio.com/docs/nodejs/nodejs-debugging)
- [Chrome DevTools Documentation](https://developers.google.com/web/tools/chrome-devtools)

Happy debugging! 🐛✨
