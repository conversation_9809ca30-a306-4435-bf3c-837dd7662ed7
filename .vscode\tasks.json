{"version": "2.0.0", "tasks": [{"label": "npm: install", "type": "npm", "script": "install", "group": "build", "presentation": {"echo": true, "reveal": "always", "focus": false, "panel": "shared"}, "problemMatcher": []}, {"label": "npm: start", "type": "npm", "script": "start", "group": {"kind": "build", "isDefault": true}, "presentation": {"echo": true, "reveal": "always", "focus": false, "panel": "shared"}, "problemMatcher": []}, {"label": "electron: dev", "type": "shell", "command": "npx", "args": ["electron", ".", "--enable-logging"], "group": "build", "presentation": {"echo": true, "reveal": "always", "focus": false, "panel": "shared"}, "problemMatcher": []}]}