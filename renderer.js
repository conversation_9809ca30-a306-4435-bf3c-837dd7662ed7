const dropZone = document.getElementById('drop-zone');
const statusDiv = document.getElementById('status');
const dropText = document.getElementById('drop-text');

dropZone.addEventListener('dragover', (event) => {
  event.preventDefault();
  event.stopPropagation();
  dropZone.classList.add('border-blue-500', 'bg-gray-700');
  dropText.textContent = "Release to Upload";
});

dropZone.addEventListener('dragleave', (event) => {
  event.preventDefault();
  event.stopPropagation();
  dropZone.classList.remove('border-blue-500', 'bg-gray-700');
  dropText.textContent = "Drag & Drop PDF Here";
});

dropZone.addEventListener('drop', async (event) => {
  event.preventDefault();
  event.stopPropagation();
  dropZone.classList.remove('border-blue-500', 'bg-gray-700');
  dropText.textContent = "Drag & Drop PDF Here";

  const files = event.dataTransfer.files;
  if (files.length > 0 && files[0].path.toLowerCase().endsWith('.pdf')) {
    const filePath = files[0].path;
    statusDiv.innerHTML = `<p class="text-yellow-400">Extracting text from "${files[0].name}"... This may take a moment for large files.</p>`;

    const result = await window.electronAPI.convertPdf(filePath);

    if (result.success) {
      const sanitizedDomain = result.domain.toLowerCase().replace(/[^a-z0-9-]/g, '-') + '.test';
      const port = result.url.split(':').pop().split('/')[0];
      const domainUrl = `http://${sanitizedDomain}:${port}/${result.url.split('/').pop()}`;

      statusDiv.innerHTML = `
        <div class="bg-green-800 border border-green-600 p-4 rounded-lg">
            <h2 class="text-2xl font-bold text-green-300">Success!</h2>
            <p class="text-green-200 mt-2">Your document is ready for Immersive Reader.</p>
            <p class="mt-4"><strong>Local URL:</strong> <a href="${result.url}" target="_blank" class="text-blue-400 hover:underline">${result.url}</a></p>
            <p class="mt-1 text-sm text-gray-300">(Clicking this link will open the simple webpage. Use Edge's Immersive Reader on that page.)</p>

            <div class="mt-6 text-left bg-gray-900 p-4 rounded-lg">
                <h3 class="font-bold text-lg text-yellow-300">Optional: Use a .test domain</h3>
                <p class="mt-2 text-gray-300">1. Open your computer's 'hosts' file with admin/sudo permissions.</p>
                <p class="text-gray-400 text-sm">(macOS/Linux: <code class="bg-gray-700 px-1 rounded">/etc/hosts</code> | Windows: <code class="bg-gray-700 px-1 rounded">C:\\Windows\\System32\\drivers\\etc\\hosts</code>)</p>
                <p class="mt-2 text-gray-300">2. Add the following line to the end of the file and save:</p>
                <pre class="bg-gray-800 p-2 rounded mt-2 text-white">127.0.0.1   ${sanitizedDomain}</pre>
                <p class="mt-3 text-gray-300">3. You can now access your site at: <a href="${domainUrl}" target="_blank" class="text-blue-400 hover:underline">${domainUrl}</a></p>
            </div>
        </div>
      `;
    } else {
      statusDiv.innerHTML = `<div class="bg-red-800 border border-red-600 p-4 rounded-lg"><p class="text-red-300 font-bold">Error:</p><p class="mt-2 text-red-200">${result.message}</p></div>`;
    }

  } else {
    statusDiv.innerHTML = `<p class="text-red-400">Please drop a single PDF file.</p>`;
  }
});
