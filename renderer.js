// Renderer process script
// This script runs in the renderer process and has access to the DOM

document.addEventListener('DOMContentLoaded', () => {
  console.log('Renderer script loaded');
  
  // Initialize the application
  initializeApp();
});

function initializeApp() {
  // Set up event listeners
  setupEventListeners();
  
  // Initialize UI components
  initializeUI();
}

function setupEventListeners() {
  // Example: Button click handlers
  const buttons = document.querySelectorAll('button');
  buttons.forEach(button => {
    button.addEventListener('click', handleButtonClick);
  });
  
  // Example: Form submissions
  const forms = document.querySelectorAll('form');
  forms.forEach(form => {
    form.addEventListener('submit', handleFormSubmit);
  });
}

function handleButtonClick(event) {
  const button = event.target;
  const action = button.dataset.action;
  
  switch (action) {
    case 'open-file':
      openFile();
      break;
    case 'save-file':
      saveFile();
      break;
    default:
      console.log('Button clicked:', button.textContent);
  }
}

function handleFormSubmit(event) {
  event.preventDefault();
  const form = event.target;
  const formData = new FormData(form);
  
  console.log('Form submitted:', Object.fromEntries(formData));
}

function initializeUI() {
  // Initialize any UI components here
  updateStatus('Application ready');
}

function updateStatus(message) {
  const statusElement = document.getElementById('status');
  if (statusElement) {
    statusElement.textContent = message;
  }
}

async function openFile() {
  try {
    if (window.electronAPI) {
      const result = await window.electronAPI.openFile();
      console.log('File opened:', result);
    }
  } catch (error) {
    console.error('Error opening file:', error);
  }
}

async function saveFile() {
  try {
    if (window.electronAPI) {
      const content = 'Sample file content';
      const result = await window.electronAPI.saveFile(content);
      console.log('File saved:', result);
    }
  } catch (error) {
    console.error('Error saving file:', error);
  }
}
